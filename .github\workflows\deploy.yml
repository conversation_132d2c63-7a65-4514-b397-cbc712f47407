name: CD

on:
  workflow_dispatch:

permissions: read-all

jobs:
  deploy:
    name: Deploy API to Production
    if: ${{ github.repository == 'sws2apps/sws2apps-api' && github.ref == 'refs/heads/main' }}
    environment:
      name: Prod.env
      url: https://api.sws2apps.com
    runs-on: ubuntu-latest

    steps:
      - name: Install Koyeb CLI
        run: |
          curl -fsSL https://raw.githubusercontent.com/koyeb/koyeb-cli/master/install.sh | sh

      - name: Checkout for release preparation
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          ref: main
          persist-credentials: false

      - name: Use Node.js LTS version
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020
        with:
          node-version: lts/Jod

      - name: Install dependencies
        run: npm ci

      - name: Semantic Release
        id: semantic
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
        run: npx semantic-release

      - name: Deploy API to Koeyb
        env:
          KOEYB_SERVICE_NAME: ${{ vars.KOEYB_SERVICE_NAME }}
          KOEYB_API_TOKEN: ${{ secrets.KOEYB_API_TOKEN }}
        run: |
          export PATH="$HOME/.koyeb/bin:$PATH"
          koyeb service redeploy $KOEYB_SERVICE_NAME --token $KOEYB_API_TOKEN
