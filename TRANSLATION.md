# Scheduling Workbox System (sws2apps) API Translation Guide

## Get started

1. Open the [sws2apps-api](https://crowdin.com/project/sws2apps-api) project on Crowdin
2. Find your locale and start translation. Find more details in [guide for volunteer translators](https://support.crowdin.com/for-volunteer-translators/)

All translated and approved content will be pushed to this repo automatically. You don't need to create any PRs with translation.

Original source can be found in [/locales/en](https://github.com/sws2apps/sws2apps-api/tree/main/src/locales/en). If you find any problem with original source, please create a PR with changes directly to `/locales/en`. Crowdin automatically pull all updates within 3 hours.

### I can't find my language on Crowdin

Please create a [new issue](https://github.com/sws2apps/sws2apps-api/issues/new?template=new_language_request.yml) in this repo. We would be happy to add the new language so that you can start the translation on Crowdin.