# Scheduling Workbox System (sws2apps) API

[![CD](https://github.com/sws2apps/sws2apps-api/actions/workflows/deploy.yml/badge.svg)](https://github.com/sws2apps/sws2apps-api/actions/workflows/deploy.yml)

Backend service for all sws2apps applications.

## Localization

We support the localization of the email message generated by this API, based on UI language selected by the user. The translation process is handled on [Crowdin](https://crowdin.com/project/sws2apps-api). To help with localization, please read the [TRANSLATION](./TRANSLATION.md) guide.
