version: 2
updates:
  # Maintain dependencies for GitHub Actions
  - package-ecosystem: 'github-actions'
    directory: '/'
    target-branch: main
    schedule:
      interval: 'daily'
    commit-message:
      prefix: 'chore(actions)'

  - package-ecosystem: 'github-actions'
    target-branch: 'main-e2e'
    directory: '/'
    schedule:
      interval: 'daily'
    commit-message:
      prefix: 'chore(actions)'

  # Maintain dependencies for npm
  - package-ecosystem: 'npm'
    directory: '/'
    target-branch: main
    schedule:
      interval: 'daily'
    allow:
      - dependency-type: 'production'
      - dependency-type: 'development'
    commit-message:
      prefix: 'feat(deps)'
      prefix-development: 'build(deps)'

  - package-ecosystem: 'npm'
    directory: '/'
    target-branch: main-e2e
    schedule:
      interval: 'daily'
    allow:
      - dependency-type: 'production'
      - dependency-type: 'development'
    commit-message:
      prefix: 'feat(deps)'
      prefix-development: 'build(deps)'
