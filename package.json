{"name": "sws2apps-api", "version": "3.34.0", "description": "Node apps to handle backend operations for all sws2apps services", "main": "index.js", "scripts": {"dev": "cross-env NODE_ENV=development nodemon src/index.ts", "dev:inspect": "cross-env NODE_ENV=development nodemon --inspect src/index.ts", "build": "rimraf dist && tsc", "start": "cross-env NODE_ENV=production node dist/index.js", "start:dev": "cross-env NODE_ENV=development node dist/index.js", "setup:emulators": "firebase init emulators", "start:emulators": "firebase emulators:start --project=organized-local", "lint": "eslint src"}, "keywords": [], "author": "", "license": "MIT", "type": "module", "engines": {"node": "22.x", "npm": ">=10"}, "dependencies": {"@crowdin/crowdin-api-client": "^1.46.0", "@logtail/node": "^0.5.5", "bcrypt": "^6.0.0", "compression": "^1.8.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cross-env": "^10.0.0", "crypto-es": "^2.1.0", "cryptr": "^6.3.0", "dayjs": "^1.11.13", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "firebase-admin": "^13.4.0", "geoip-lite": "^1.4.10", "global-jsdom": "^26.0.0", "helmet": "^8.1.0", "i18next": "^25.3.2", "i18next-http-middleware": "^3.8.0", "is-online": "^11.0.0", "jsdom": "^26.1.0", "node-2fa": "^2.0.3", "node-fetch": "^3.3.2", "node-html-parser": "^7.0.1", "nodemailer": "^7.0.5", "nodemailer-express-handlebars": "^7.0.0", "otpauth": "^9.4.0", "randomstring": "^1.3.1", "request-ip": "^3.3.0", "sanitize-html": "^2.17.0", "serve-favicon": "^2.5.1", "which-browser": "^0.7.1"}, "repository": {"type": "git", "url": "https://github.com/sws2apps/sws2apps-api.git"}, "devDependencies": {"@eslint/js": "^9.32.0", "@logtail/types": "^0.5.3", "@semantic-release/changelog": "^6.0.3", "@semantic-release/exec": "^7.1.0", "@semantic-release/git": "^10.0.1", "@types/compression": "^1.8.1", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/geoip-lite": "^1.4.4", "@types/node": "^24.1.0", "@types/nodemailer": "^6.4.17", "@types/nodemailer-express-handlebars": "^4.0.5", "@types/randomstring": "^1.3.0", "@types/request-ip": "^0.0.41", "@types/sanitize-html": "^2.16.0", "@types/serve-favicon": "^2.5.7", "eslint": "^9.32.0", "globals": "^16.3.0", "nodemon": "^3.1.10", "rimraf": "^6.0.1", "semantic-release": "^24.2.7", "tsx": "^4.20.3", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0"}}