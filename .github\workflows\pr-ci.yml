name: Lint Check

on:
  pull_request:
    branches: [main]

permissions: read-all

jobs:
  cypress-run:
    name: Run linting
    runs-on: ubuntu-latest

    permissions:
      actions: read
      contents: read

    steps:
      - name: Checkout for linting
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          persist-credentials: false

      - name: Use Node.js LTS version
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020
        with:
          node-version: lts/Jod

      - name: Install Dependencies
        run: npm ci

      - name: Check lint
        run: npm run lint
