# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# misc
cong_backup
**/S-34/*.json
.idx

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage
.nyc_output/

# production
/build
/dist

# misc
.firebaserc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
jwt.js
sample.json
storage.rules

npm-debug.log*
yarn-debug.log*
yarn-error.log*
*-debug.log

.idea