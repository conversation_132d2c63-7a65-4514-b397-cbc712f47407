name: CD-Redeploy

on:
  workflow_dispatch:

permissions: read-all

jobs:
  redeploy_render:
    name: Redeploy API to Production
    if: ${{ github.repository == 'sws2apps/sws2apps-api' && github.ref == 'refs/heads/main' }}
    environment:
      name: Prod.env
      url: https://api.sws2apps.com
    runs-on: ubuntu-latest

    steps:
      - name: Install Koyeb CLI
        run: |
          curl -fsSL https://raw.githubusercontent.com/koyeb/koyeb-cli/master/install.sh | sh

      - name: Deploy API to Koeyb
        env:
          KOEYB_SERVICE_NAME: ${{ vars.KOEYB_SERVICE_NAME }}
          KOEYB_API_TOKEN: ${{ secrets.KOEYB_API_TOKEN }}
        run: |
          export PATH="$HOME/.koyeb/bin:$PATH"
          koyeb service redeploy $KOEYB_SERVICE_NAME --token $KOEYB_API_TOKEN
